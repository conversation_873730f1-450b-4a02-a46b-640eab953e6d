cd erc20

# Create hardhat config with auto-detected private key
cat > hardhat.config.js << EOF
require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-ignition-ethers");

module.exports = {
  solidity: "0.8.28",
  networks: {
    ethermint: {
      url: "http://localhost:8545",
      accounts: [************],
      chainId: 9000,
      gas: 2100000,
      gasPrice: **********,
    }
  }
};
EOF