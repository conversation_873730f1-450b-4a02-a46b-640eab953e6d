#!/bin/bash
set -e
sleep 15

# Force clean start
echo "==> Cleaning previous data..."
rm -rf ~/.ethermintd*

# 1. Initialize node
echo "==> Initializing ethermintd..."
cd ethermint
./init.sh
cd ../

# 2. Start node background
echo "==> Starting ethermintd..."
ethermintd start --metrics --pruning=nothing --evm.tracer=json --trace --log_level info --minimum-gas-prices=0.0001aphoton --json-rpc.api eth,txpool,personal,net,debug,web3,miner --api.enable &

ETHERMINT_PID=$!
echo "==> ethermintd PID: $ETHERMINT_PID"

# Wait for node to start
sleep 15

# Get account info automatically
echo "==> Getting account info..."
MAIN_PRIVATE_KEY=$(ethermintd keys unsafe-export-eth-key mykey --keyring-backend test)
MAIN_ETH_ADDRESS=$(ethermintd debug addr $(ethermintd keys show mykey -a --keyring-backend test) | grep "Address (EIP-55):" | awk '{print $3}')

echo "Main private key: $MAIN_PRIVATE_KEY"
echo "Main ETH address: $MAIN_ETH_ADDRESS"

# Deploy contract with auto-generated config
cd erc20
npm install


echo "==> Writing hardhat.config.js with real value..."
echo "Private key to be written: $MAIN_PRIVATE_KEY"

cat <<EOF > hardhat.config.js
require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-ignition-ethers");

module.exports = {
  solidity: "0.8.28",
  networks: {
    ethermint: {
      url: "http://localhost:8545",
      accounts: ["${MAIN_PRIVATE_KEY}"],
      chainId: 9000,
      gas: 2100000,
      gasPrice: **********
    }
  }
};
EOF


echo "Main ETH address: $MAIN_ETH_ADDRESS"

# Update ignition module with auto-detected address
cat > ignition/modules/MyERC20Token.js << EOF
const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("MyERC20TokenModule", (m) => {
  const tokenName = m.getParameter("tokenName", "DataCoinERC");
  const tokenSymbol = m.getParameter("tokenSymbol", "ERC");
  const initialSupply = m.getParameter("initialSupply", 1000000);
  const initialOwner = m.getParameter("initialOwner", "${MAIN_ETH_ADDRESS}");

  const token = m.contract("MyERC20Token", [
    tokenName,
    tokenSymbol,
    initialSupply,
    initialOwner
  ]);

  return { token };
});
EOF

echo "==> Deploying contract..."
yes | npx hardhat ignition deploy ./ignition/modules/MyERC20Token.js --network ethermint
sleep 20

# Lấy địa chỉ contract từ deployed_addresses.json
DEPLOYED_JSON="./ignition/deployments/chain-9000/deployed_addresses.json"
if [ -f "$DEPLOYED_JSON" ]; then
  CONTRACT_ADDRESS=$(jq -r '."MyERC20TokenModule#MyERC20Token"' "$DEPLOYED_JSON")

  # Ghi vào file .env.local của dapp
  ENV_FILE="../dapp/.env.local"
  echo "NEXT_PUBLIC_CONTRACT_ADDRESS=$CONTRACT_ADDRESS" > "$ENV_FILE"
  echo "==> Đã ghi địa chỉ contract vào $ENV_FILE: $CONTRACT_ADDRESS"
else
  echo "❌ Không tìm thấy file: $DEPLOYED_JSON"
fi
sleep 10

echo "==> Dừng ethermintd..."
kill "$ETHERMINT_PID"
wait "$ETHERMINT_PID" 2>/dev/null

echo "==> Đã dừng ethermintd"

